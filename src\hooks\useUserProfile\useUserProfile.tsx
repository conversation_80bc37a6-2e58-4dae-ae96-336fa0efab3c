import { useState, useEffect, useCallback, useRef } from "react";
import { useSDK } from "../useSDK";
import { useToast } from "../../components/Toast";

export interface IUserProfile {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  photo?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  country?: string | null;
  member_since?: string | null;
  date_of_birth?: string | null;
  gender?: string | null;
  bio?: string | null;
  referral_code?: string | null;
  referral_type?: string | null;
  referrer_name?: string | null;
  is_delivery_agent: boolean;
  role_id: string;
  status: string;
  verify: string;
  created_at: string;
  updated_at: string;
}

export interface IDeliveryAgentStatus {
  status: "not_registered" | "pending" | "active" | "rejected";
  canReapply: boolean;
  applicationId?: number;
  rejectionReason?: string;
}

export const useUserProfile = () => {
  const { sdk } = useSDK();
  const { error: showError } = useToast();

  const [profile, setProfile] = useState<IUserProfile | null>(null);
  const [deliveryAgentStatus, setDeliveryAgentStatus] =
    useState<IDeliveryAgentStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use refs to store the latest values to avoid dependency issues
  const sdkRef = useRef(sdk);
  const showErrorRef = useRef(showError);
  const isInitializedRef = useRef(false);

  // Update refs when values change
  sdkRef.current = sdk;
  showErrorRef.current = showError;

  const fetchProfile = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await sdkRef.current.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile",
        method: "GET",
      });

      if (!response.error && response.data) {
        setProfile(response.data);
      } else {
        setError(response.message || "Failed to fetch profile");
        showErrorRef.current(response.message || "Failed to fetch profile");
      }
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to fetch profile";
      setError(errorMessage);
      showErrorRef.current(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchDeliveryAgentStatus = useCallback(async () => {
    try {
      const response = await sdkRef.current.request({
        endpoint: "/v2/api/ebadollar/custom/member/delivery-agent/status",
        method: "GET",
      });

      if (!response.error && response.data) {
        setDeliveryAgentStatus(response.data);
      } else {
        // If no application found, user is not registered
        setDeliveryAgentStatus({
          status: "not_registered",
          canReapply: true,
        });
      }
    } catch (error: any) {
      console.error("Error fetching delivery agent status:", error);
      // Default to not registered if there's an error
      setDeliveryAgentStatus({
        status: "not_registered",
        canReapply: true,
      });
    }
  }, []);

  const updateProfile = useCallback(async (data: Partial<IUserProfile>) => {
    try {
      const response = await sdkRef.current.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile",
        method: "PUT",
        body: data,
      });

      if (!response.error) {
        // Update local profile state
        setProfile((prev) => (prev ? { ...prev, ...data } : null));
        return { success: true, data: response.data };
      } else {
        return {
          success: false,
          error: response.message || "Failed to update profile",
        };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || "Failed to update profile",
      };
    }
  }, []);

  const refreshProfile = useCallback(() => {
    fetchProfile();
    fetchDeliveryAgentStatus();
  }, []);

  // Computed properties
  const isDeliveryAgent = deliveryAgentStatus?.status === "active" || false;
  const isApprovedDeliveryAgent =
    deliveryAgentStatus?.status === "active";
  const fullName = profile
    ? `${profile.first_name} ${profile.last_name}`.trim()
    : "";

  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      fetchProfile();
      fetchDeliveryAgentStatus();
    }
  }, []);

  return {
    // Profile data
    profile,
    loading,
    error,

    // Delivery agent specific data
    deliveryAgentStatus,
    isDeliveryAgent,
    isApprovedDeliveryAgent,

    // Computed properties
    fullName,

    // Actions
    updateProfile,
    refreshProfile,
    fetchProfile,
    fetchDeliveryAgentStatus,
  };
};

export default useUserProfile;
