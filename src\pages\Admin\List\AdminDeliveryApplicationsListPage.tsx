import React, { useState, useCallback, useEffect, ChangeEvent } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { IApplication } from "./types";
import SearchIcon from "../../../assets/svgs/SearchIcon";
import { Modal } from "../../../components/Modal";
import ReferencesModal from "./ReferencesModal";
import DeliveryVerifyDocumentsModal from "./DeliveryVerifyDocumentsModal";
import { useSDK } from "../../../hooks/useSDK";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import MkdInputV2 from "../../../components/MkdInputV2";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { ActionConfirmationModal } from "../../../components/ActionConfirmationModal";
import { useToast } from "../../../components/Toast";

const statusColors: Record<string, string> = {
  Active: "bg-green-100 text-green-800",
  Pending: "bg-yellow-100 text-yellow-800",
  Rejected: "bg-red-100 text-red-800",
};

interface Pagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const AdminDeliveryApplicationsListPage = () => {
  const [isReferencesModalOpen, setIsReferencesModalOpen] = useState(false);
  const [isVerifyModalOpen, setIsVerifyModalOpen] = useState(false);
  const [selectedApplicationId, setSelectedApplicationId] = useState<
    number | null
  >(null);
  const [action, setAction] = useState<"approve" | "reject" | null>(null);
  const [isActionLoading, setIsActionLoading] = useState(false);

  const { sdk } = useSDK();
  const toast = useToast();
  const [applications, setApplications] = useState<IApplication[]>([]);
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState(true);

  const [uiFilters, setUiFilters] = useState({
    search: "",
    status: "all",
    sort_by: "created_at",
    sort_order: "desc",
  });

  const [apiParams, setApiParams] = useState({
    search: "",
    status: "all",
    sort_by: "created_at",
    sort_order: "desc",
    page: 1,
    limit: 10,
  });

  const fetchApplications = useCallback(async () => {
    setLoading(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/delivery-applications`,
        method: "GET",
        params: apiParams,
      });

      if (!response.error) {
        setApplications(response.data);
        setPagination((response as any).pagination);
      }
    } catch (error: any) {
      console.error("Error fetching applications:", error);
    } finally {
      setLoading(false);
    }
  }, [apiParams]);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const handleFilterInputChange = (key: string, value: string | number) => {
    setUiFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    setApiParams({ ...apiParams, ...uiFilters, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setApiParams((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setApiParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  const openReferencesModal = (id: number) => {
    setSelectedApplicationId(id);
    setIsReferencesModalOpen(true);
  };
  const closeReferencesModal = () => setIsReferencesModalOpen(false);

  const openVerifyModal = (id: number) => {
    setSelectedApplicationId(id);
    setIsVerifyModalOpen(true);
  };
  const closeVerifyModal = () => setIsVerifyModalOpen(false);

  const handleActionClick = (id: number, actionType: "approve" | "reject") => {
    setSelectedApplicationId(id);
    setAction(actionType);
  };

  const handleSuccess = async () => {
    if (!selectedApplicationId || !action) return;

    setIsActionLoading(true);
    try {
      const status = action === "approve" ? "active" : "rejected";
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/delivery-applications/${selectedApplicationId}/status`,
        method: "PUT",
        body: { status },
      });

      if (!response.error) {
        toast.success(`Application ${action}d successfully!`);
        fetchApplications();
      } else {
        toast.error(response.message || `Failed to ${action} application`);
      }
    } catch (error: any) {
      toast.error(error?.message || `Error ${action}ing application`);
    } finally {
      setIsActionLoading(false);
      setAction(null);
      setSelectedApplicationId(null);
    }
  };

  const statusOptions = [
    { value: "all", label: "All Statuses" },
    { value: "pending", label: "Pending" },
    { value: "active", label: "Active" },
    { value: "rejected", label: "Rejected" },
  ];

  const sortOptions = [
    { value: "created_at-desc", label: "Newest" },
    { value: "created_at-asc", label: "Oldest" },
  ];

  const handleSortChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const [sort_by, sort_order] = e.target.value.split("-");
    setUiFilters((prev) => ({ ...prev, sort_by, sort_order }));
  };

  return (
    <AdminWrapper>
      <div className="p-6 bg-gray-50 min-h-screen">
        <header className="mb-6">
          <h1 className="text-3xl font-bold text-[#1E293B]">
            Delivery Agent Applications
          </h1>
          <p className="text-gray-500 mt-1">
            Review and manage delivery agent applications
          </p>
        </header>

        <div className="bg-white rounded-xl shadow-md p-4 mb-6">
          <div className="flex items-end gap-4">
            <div className="flex-[2]">
              <div className="">
               
                <MkdInputV2
                  value={uiFilters.search}
                  onChange={(e) =>
                    handleFilterInputChange("search", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field
                      placeholder="Search applicants"
                      className="w-full pl-10 pr-4 py-2 !border !border-[#D1D5DB] !rounded-md !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                    />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
            </div>

            <div className="flex-[1]">
              <MkdInputV2
                type="mapping"
                mapping={statusOptions.reduce(
                  (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                  {}
                )}
                value={uiFilters.status}
                onChange={(e) =>
                  handleFilterInputChange("status", e.target.value)
                }
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] !rounded-md py-2 px-4 !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] w-full" />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>

            <div className="flex-[1]">
              <MkdInputV2
                type="mapping"
                mapping={sortOptions.reduce(
                  (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                  {}
                )}
                value={`${uiFilters.sort_by}-${uiFilters.sort_order}`}
                onChange={handleSortChange}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] !rounded-md py-2 px-4 !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] w-full" />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
            <div>
              <InteractiveButton
                onClick={handleApplyFilters}
                className="!bg-[#0F2C59] text-white font-semibold !py-4 px-6 rounded-md hover:bg-opacity-90 !h-full transition w-full"
              >
                Apply filters
              </InteractiveButton>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-500">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3">
                  User
                </th>
                <th scope="col" className="px-6 py-3">
                  Emergency Contact
                </th>
                <th scope="col" className="px-6 py-3">
                  References
                </th>
                <th scope="col" className="px-6 py-3">
                  Status
                </th>
                <th scope="col" className="px-6 py-3">
                  Rating
                </th>
                <th scope="col" className="px-6 py-3">
                  Documents Status
                </th>
                <th scope="col" className="px-6 py-3">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <tr key={index}>
                    <td colSpan={7} className="px-6 py-4">
                      <Skeleton className="h-4 w-full" />
                    </td>
                  </tr>
                ))
              ) : applications?.length > 0 ? (
                applications?.map((app) => (
                  <tr key={app.id} className="bg-white border-b">
                    <td className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
                      {app.user}
                    </td>
                    <td className="px-6 py-4">{app.emergencyContact}</td>
                    <td className="px-6 py-4">
                      <button
                        onClick={() => openReferencesModal(app.id)}
                        className="font-medium text-blue-600 hover:underline"
                      >
                        {/* {app.references} */}
                        View
                      </button>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          statusColors[app.status]
                        }`}
                      >
                        {app.status}
                      </span>
                    </td>
                    <td className="px-6 py-4">{app.rating}</td>
                    <td className="px-6 py-4">
                      <button
                        onClick={() => openVerifyModal(app.id)}
                        className="font-medium text-blue-600 hover:underline"
                      >
                        {app.documentsStatus}
                      </button>
                    </td>
                    <td className="px-6 py-4 flex items-center space-x-3">
                      <button
                        onClick={() => handleActionClick(app.id, "approve")}
                        className="font-medium text-blue-600 hover:underline"
                        disabled={app.status === "Active"}
                      >
                        Approve
                      </button>
                      <button
                        onClick={() => handleActionClick(app.id, "reject")}
                        className="font-medium text-[#E63946] hover:underline"
                        disabled={app.status === "Rejected"}
                      >
                        Reject
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="text-center py-10">
                    No applications found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {pagination && pagination.total > 0 && (
          <PaginationBar
            currentPage={pagination.page}
            pageCount={pagination.num_pages}
            pageSize={pagination.limit}
            canPreviousPage={pagination.has_prev}
            canNextPage={pagination.has_next}
            updatePageSize={handleLimitChange}
            updateCurrentPage={handlePageChange}
            startSize={5}
            multiplier={5}
            canChangeLimit={true}
          />
        )}
      </div>
      {isReferencesModalOpen && selectedApplicationId && (
        <Modal
          isOpen={isReferencesModalOpen}
          modalCloseClick={closeReferencesModal}
          title=""
          modalHeader={false}
          classes={{
            modal: "h-full",
            modalDialog: "w-[90%] max-w-4xl bg-white !p-6",
            modalContent: "!p-0 !m-0",
          }}
        >
          <ReferencesModal
            onClose={closeReferencesModal}
            applicationId={selectedApplicationId}
          />
        </Modal>
      )}
      {isVerifyModalOpen && selectedApplicationId && (
        <Modal
          isOpen={isVerifyModalOpen}
          modalCloseClick={closeVerifyModal}
          title=""
          modalHeader={false}
          classes={{
            modal: "h-full",
            modalDialog: "w-[90%] max-w-7xl bg-white !p-6",
            modalContent: "!p-0 !m-0",
          }}
        >
          <DeliveryVerifyDocumentsModal
            onClose={closeVerifyModal}
            applicationId={selectedApplicationId}
          />
        </Modal>
      )}
      {action && (
        <ActionConfirmationModal
          isOpen={!!action}
          onClose={() => setAction(null)}
          onSuccess={handleSuccess}
          title={String(`Confirm ${action || ""}`)}
          customMessage={String(
            `Are you sure you want to ${action || ""} this application?`
          )}
          isLoading={isActionLoading}
        />
      )}
    </AdminWrapper>
  );
};

export default AdminDeliveryApplicationsListPage;
